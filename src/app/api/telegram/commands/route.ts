import { NextResponse } from 'next/server';
import { setBotCommands } from '@/lib/telegramBotSetup';
import logger, { getRequestId } from '@/lib/logger';
import { AppError, createApiHandler } from '@/lib/errorUtils';

/**
 * Set Telegram bot commands
 * POST: Set the bot commands in Telegram's menu
 * GET: Check current bot commands
 */
export const POST = createApiHandler(async (req: Request) => {
  const requestId = getRequestId();
  logger.info('Bot commands setup requested', { requestId });

  try {
    // Verify bot token
    const botToken = process.env.NOTIFICATION_BOT_TOKEN;
    if (!botToken) {
      throw AppError.validation(
        'Telegram bot token not configured',
        { missingEnvVar: 'NOTIFICATION_BOT_TOKEN' }
      );
    }

    logger.info('Setting bot commands');

    // Set bot commands
    const success = await setBotCommands(botToken);

    if (!success) {
      throw AppError.externalService(
        'Failed to set bot commands',
        { service: 'telegram' }
      );
    }

    logger.info('Bot commands set successfully', { requestId });

    return NextResponse.json({
      success: true,
      message: 'Bot commands set successfully',
      commands: [
        { command: 'start', description: 'Start the bot and see welcome message' },
        { command: 'latest', description: 'Get latest discovered tokens (e.g. /latest 5)' },
        { command: 'search', description: 'Search tokens by ticker or source (e.g. /search BTC)' },
        { command: 'help', description: 'Show help and available commands' }
      ],
      requestId
    });

  } catch (error) {
    throw error;
  }
});

/**
 * Get current bot commands
 */
export const GET = createApiHandler(async (req: Request) => {
  const requestId = getRequestId();
  logger.info('Bot commands check requested', { requestId });

  try {
    // Verify bot token
    const botToken = process.env.NOTIFICATION_BOT_TOKEN;
    if (!botToken) {
      throw AppError.validation(
        'Telegram bot token not configured',
        { missingEnvVar: 'NOTIFICATION_BOT_TOKEN' }
      );
    }

    // Get current commands from Telegram
    const url = `https://api.telegram.org/bot${botToken}/getMyCommands`;
    const response = await fetch(url);
    const result = await response.json();

    if (!result.ok) {
      throw AppError.externalService(
        'Failed to get bot commands from Telegram',
        { service: 'telegram', error: result.description }
      );
    }

    logger.info('Successfully retrieved bot commands', { 
      commandCount: result.result?.length || 0,
      requestId 
    });

    return NextResponse.json({
      success: true,
      commands: result.result || [],
      commandCount: result.result?.length || 0,
      requestId
    });

  } catch (error) {
    throw error;
  }
});
