// Set commands via the production API
async function setCommandsViaAPI() {
  try {
    console.log('🔧 Setting commands via production API...');
    
    // First check current status
    const statusResponse = await fetch('https://bs-bot-two.vercel.app/api/telegram/setup');
    const status = await statusResponse.json();
    console.log('Current status:', status);
    
    // Trigger bot setup which includes setting commands
    const setupResponse = await fetch('https://bs-bot-two.vercel.app/api/telegram/setup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        webhookUrl: 'https://bs-bot-two.vercel.app/api/telegram/webhook'
      })
    });
    
    const setupResult = await setupResponse.json();
    console.log('Setup result:', setupResult);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

setCommandsViaAPI();
