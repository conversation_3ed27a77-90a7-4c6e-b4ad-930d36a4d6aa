// Script to check and set Telegram bot commands
const BOT_TOKEN = '**********************************************';

const commands = [
  {
    command: 'start',
    description: 'Start the bot and see welcome message'
  },
  {
    command: 'latest',
    description: 'Get latest discovered tokens (e.g. /latest 5)'
  },
  {
    command: 'search',
    description: 'Search tokens by ticker or source (e.g. /search BTC)'
  },
  {
    command: 'help',
    description: 'Show help and available commands'
  }
];

async function checkCommands() {
  try {
    console.log('🔍 Checking current bot commands...');
    const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/getMyCommands`);
    const result = await response.json();
    
    if (result.ok) {
      console.log('✅ Current commands:', result.result);
      return result.result;
    } else {
      console.error('❌ Error getting commands:', result.description);
      return null;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    return null;
  }
}

async function setCommands() {
  try {
    console.log('🔧 Setting bot commands...');
    const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/setMyCommands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ commands })
    });
    
    const result = await response.json();
    
    if (result.ok) {
      console.log('✅ Commands set successfully!');
      return true;
    } else {
      console.error('❌ Error setting commands:', result.description);
      return false;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

async function main() {
  const currentCommands = await checkCommands();
  
  if (!currentCommands || currentCommands.length === 0) {
    console.log('📝 No commands found, setting them now...');
    await setCommands();
    
    // Check again to confirm
    console.log('\n🔍 Verifying commands were set...');
    await checkCommands();
  } else {
    console.log('✅ Commands are already set!');
  }
}

main();
