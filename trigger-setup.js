// Trigger bot setup to set commands
async function triggerSetup() {
  try {
    console.log('🔧 Triggering bot setup to set commands...');
    
    const response = await fetch('https://bs-bot-two.vercel.app/api/telegram/setup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        webhookUrl: 'https://bs-bot-two.vercel.app/api/telegram/webhook'
      })
    });
    
    const result = await response.json();
    console.log('Setup result:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ Bot setup completed successfully!');
      console.log('Commands should now be visible in the Telegram menu.');
    } else {
      console.log('❌ Setup failed:', result.error?.message || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

triggerSetup();
